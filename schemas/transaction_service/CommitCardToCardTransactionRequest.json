{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "CommitCardToCardTransactionRequest", "$protected": true, "title": "CommitCardToCardTransactionRequest", "description": "Commit Card To Card Transaction Request", "type": "object", "required": ["request", "totp"], "properties": {"totp": {"type": "string", "title": "totp code", "pattern": "^[0-9]{6}$"}, "request": {"type": "object", "required": ["amount", "asset", "source_card_number", "destination_card_number", "reference_id", "expire_month", "expire_year", "cvv"], "properties": {"amount": {"type": "string"}, "asset": {"type": "string"}, "source_card_number": {"type": "string"}, "destination_card_number": {"type": "string"}, "expire_month": {"type": "string"}, "expire_year": {"type": "string"}, "cvv": {"type": "string"}, "reference_id": {"type": "string"}}}}}