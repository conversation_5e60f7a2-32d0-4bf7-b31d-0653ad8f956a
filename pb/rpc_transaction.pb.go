// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc_transaction.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Transaction is the transaction object
type Transaction struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// source_account is the source account address for payer of the payment
	SourceAccount string `protobuf:"bytes,1,opt,name=source_account,json=sourceAccount,proto3" json:"source_account,omitempty"`
	// destination_account is the destination account address for acceptor of the payment
	DestinationAccount string `protobuf:"bytes,2,opt,name=destination_account,json=destinationAccount,proto3" json:"destination_account,omitempty"`
	// reference_id is actually idempotency key for making create draft transaction idempotent
	ReferenceId string `protobuf:"bytes,3,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
	// transaction amount
	Amount float64 `protobuf:"fixed64,4,opt,name=amount,proto3" json:"amount,omitempty"`
	// transaction asset type
	Asset string `protobuf:"bytes,5,opt,name=asset,proto3" json:"asset,omitempty"`
	// transaction description
	Description string `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	// tracing_id is the tracing id for transaction
	TracingId string `protobuf:"bytes,7,opt,name=tracing_id,json=tracingId,proto3" json:"tracing_id,omitempty"`
	// transaction metadata
	Metadata      map[string]string `protobuf:"bytes,8,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Transaction) Reset() {
	*x = Transaction{}
	mi := &file_rpc_transaction_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Transaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Transaction) ProtoMessage() {}

func (x *Transaction) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_transaction_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Transaction.ProtoReflect.Descriptor instead.
func (*Transaction) Descriptor() ([]byte, []int) {
	return file_rpc_transaction_proto_rawDescGZIP(), []int{0}
}

func (x *Transaction) GetSourceAccount() string {
	if x != nil {
		return x.SourceAccount
	}
	return ""
}

func (x *Transaction) GetDestinationAccount() string {
	if x != nil {
		return x.DestinationAccount
	}
	return ""
}

func (x *Transaction) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *Transaction) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *Transaction) GetAsset() string {
	if x != nil {
		return x.Asset
	}
	return ""
}

func (x *Transaction) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Transaction) GetTracingId() string {
	if x != nil {
		return x.TracingId
	}
	return ""
}

func (x *Transaction) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

var File_rpc_transaction_proto protoreflect.FileDescriptor

const file_rpc_transaction_proto_rawDesc = "" +
	"\n" +
	"\x15rpc_transaction.proto\x12\x02pb\"\xef\x02\n" +
	"\vTransaction\x12%\n" +
	"\x0esource_account\x18\x01 \x01(\tR\rsourceAccount\x12/\n" +
	"\x13destination_account\x18\x02 \x01(\tR\x12destinationAccount\x12!\n" +
	"\freference_id\x18\x03 \x01(\tR\vreferenceId\x12\x16\n" +
	"\x06amount\x18\x04 \x01(\x01R\x06amount\x12\x14\n" +
	"\x05asset\x18\x05 \x01(\tR\x05asset\x12 \n" +
	"\vdescription\x18\x06 \x01(\tR\vdescription\x12\x1d\n" +
	"\n" +
	"tracing_id\x18\a \x01(\tR\ttracingId\x129\n" +
	"\bmetadata\x18\b \x03(\v2\x1d.pb.Transaction.MetadataEntryR\bmetadata\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01B,Z*github.com/liveutil/transaction_service/pbb\x06proto3"

var (
	file_rpc_transaction_proto_rawDescOnce sync.Once
	file_rpc_transaction_proto_rawDescData []byte
)

func file_rpc_transaction_proto_rawDescGZIP() []byte {
	file_rpc_transaction_proto_rawDescOnce.Do(func() {
		file_rpc_transaction_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_transaction_proto_rawDesc), len(file_rpc_transaction_proto_rawDesc)))
	})
	return file_rpc_transaction_proto_rawDescData
}

var file_rpc_transaction_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_rpc_transaction_proto_goTypes = []any{
	(*Transaction)(nil), // 0: pb.Transaction
	nil,                 // 1: pb.Transaction.MetadataEntry
}
var file_rpc_transaction_proto_depIdxs = []int32{
	1, // 0: pb.Transaction.metadata:type_name -> pb.Transaction.MetadataEntry
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_rpc_transaction_proto_init() }
func file_rpc_transaction_proto_init() {
	if File_rpc_transaction_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_transaction_proto_rawDesc), len(file_rpc_transaction_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rpc_transaction_proto_goTypes,
		DependencyIndexes: file_rpc_transaction_proto_depIdxs,
		MessageInfos:      file_rpc_transaction_proto_msgTypes,
	}.Build()
	File_rpc_transaction_proto = out.File
	file_rpc_transaction_proto_goTypes = nil
	file_rpc_transaction_proto_depIdxs = nil
}
