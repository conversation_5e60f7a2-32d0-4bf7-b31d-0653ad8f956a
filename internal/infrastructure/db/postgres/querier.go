// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package postgres

import (
	"context"
)

type Querier interface {
	FindMatchesWorkflows(ctx context.Context, arg FindMatchesWorkflowsParams) ([]Workflow, error)
	FindRegexMatchesWorkflows(ctx context.Context, arg FindRegexMatchesWorkflowsParams) ([]Workflow, error)
	GetCardToCardTransactionByReferenceID(ctx context.Context, referenceID string) (CardToCardTransaction, error)
	GetDraftTransactionByIdentifier(ctx context.Context, identifier string) (TransactionDraft, error)
	GetDraftTransactionByIdentifierAndReference(ctx context.Context, arg GetDraftTransactionByIdentifierAndReferenceParams) (TransactionDraft, error)
	SelectAllActiveWorkflows(ctx context.Context) ([]Workflow, error)
	SoftDeleteCardToCardTransaction(ctx context.Context, identifier string) error
	SoftDeleteDraftTransaction(ctx context.Context, identifier string) error
	UpsertCardToCardTransaction(ctx context.Context, arg UpsertCardToCardTransactionParams) (CardToCardTransaction, error)
	UpsertTransaction(ctx context.Context, arg UpsertTransactionParams) (TransactionDraft, error)
}

var _ Querier = (*Queries)(nil)
