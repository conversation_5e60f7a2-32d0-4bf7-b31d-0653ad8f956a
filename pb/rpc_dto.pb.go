// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc_dto.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// DraftTransactionRequest is the request object for draft transaction
type DraftTransactionRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// payer_identifier is the mobile / national id / card no of the payer
	PayerIdentifier string `protobuf:"bytes,1,opt,name=payer_identifier,json=payerIdentifier,proto3" json:"payer_identifier,omitempty"`
	// draft_type is the type of draft transaction
	DraftType string `protobuf:"bytes,2,opt,name=draft_type,json=draftType,proto3" json:"draft_type,omitempty"`
	// destination_account is the destination account address for acceptor of the payment
	DestinationAccount string `protobuf:"bytes,3,opt,name=destination_account,json=destinationAccount,proto3" json:"destination_account,omitempty"`
	// card_number is the card number of the payer
	CardNumber string `protobuf:"bytes,4,opt,name=card_number,json=cardNumber,proto3" json:"card_number,omitempty"`
	// amount is the amount to be paid
	Amount string `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
	// asset is the asset type of the amount
	Asset string `protobuf:"bytes,6,opt,name=asset,proto3" json:"asset,omitempty"`
	// description is the description of the payment
	Description string `protobuf:"bytes,7,opt,name=description,proto3" json:"description,omitempty"`
	// reference_id is actually idempotency key for making create draft transaction idempotent
	ReferenceId string `protobuf:"bytes,8,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
	// metadata is the metadata of the transaction
	Metadata      map[string]string `protobuf:"bytes,9,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DraftTransactionRequest) Reset() {
	*x = DraftTransactionRequest{}
	mi := &file_rpc_dto_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DraftTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DraftTransactionRequest) ProtoMessage() {}

func (x *DraftTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DraftTransactionRequest.ProtoReflect.Descriptor instead.
func (*DraftTransactionRequest) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{0}
}

func (x *DraftTransactionRequest) GetPayerIdentifier() string {
	if x != nil {
		return x.PayerIdentifier
	}
	return ""
}

func (x *DraftTransactionRequest) GetDraftType() string {
	if x != nil {
		return x.DraftType
	}
	return ""
}

func (x *DraftTransactionRequest) GetDestinationAccount() string {
	if x != nil {
		return x.DestinationAccount
	}
	return ""
}

func (x *DraftTransactionRequest) GetCardNumber() string {
	if x != nil {
		return x.CardNumber
	}
	return ""
}

func (x *DraftTransactionRequest) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *DraftTransactionRequest) GetAsset() string {
	if x != nil {
		return x.Asset
	}
	return ""
}

func (x *DraftTransactionRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *DraftTransactionRequest) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *DraftTransactionRequest) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// DraftTransactionResponse is the response object for draft transaction
type DraftTransactionResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// whether the request returns error or no.
	Error bool `protobuf:"varint,1,opt,name=error,proto3" json:"error,omitempty"`
	// error message if occurred or success message
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// payment draft id
	DraftTransaction *DraftTransaction `protobuf:"bytes,3,opt,name=draft_transaction,json=draftTransaction,proto3" json:"draft_transaction,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *DraftTransactionResponse) Reset() {
	*x = DraftTransactionResponse{}
	mi := &file_rpc_dto_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DraftTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DraftTransactionResponse) ProtoMessage() {}

func (x *DraftTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DraftTransactionResponse.ProtoReflect.Descriptor instead.
func (*DraftTransactionResponse) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{1}
}

func (x *DraftTransactionResponse) GetError() bool {
	if x != nil {
		return x.Error
	}
	return false
}

func (x *DraftTransactionResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *DraftTransactionResponse) GetDraftTransaction() *DraftTransaction {
	if x != nil {
		return x.DraftTransaction
	}
	return nil
}

// CommitDraftTransactionRequest is the request object for commit draft transaction
type CommitDraftTransactionRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// payment draft id
	PaymentDraftIdentifier string `protobuf:"bytes,1,opt,name=payment_draft_identifier,json=paymentDraftIdentifier,proto3" json:"payment_draft_identifier,omitempty"`
	// reference_id is actually idempotency key for making create draft transaction idempotent
	ReferenceId string `protobuf:"bytes,2,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
	// totp code
	Totp string `protobuf:"bytes,3,opt,name=totp,proto3" json:"totp,omitempty"`
	// description is the description of the payment
	Description   string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommitDraftTransactionRequest) Reset() {
	*x = CommitDraftTransactionRequest{}
	mi := &file_rpc_dto_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommitDraftTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommitDraftTransactionRequest) ProtoMessage() {}

func (x *CommitDraftTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommitDraftTransactionRequest.ProtoReflect.Descriptor instead.
func (*CommitDraftTransactionRequest) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{2}
}

func (x *CommitDraftTransactionRequest) GetPaymentDraftIdentifier() string {
	if x != nil {
		return x.PaymentDraftIdentifier
	}
	return ""
}

func (x *CommitDraftTransactionRequest) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *CommitDraftTransactionRequest) GetTotp() string {
	if x != nil {
		return x.Totp
	}
	return ""
}

func (x *CommitDraftTransactionRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// CommitDraftTransactionResponse is the response object for commit draft transaction
type CommitDraftTransactionResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// whether the request returns error or no.
	Error bool `protobuf:"varint,1,opt,name=error,proto3" json:"error,omitempty"`
	// error message if occurred or success message
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// transaction is the transaction object
	Transaction   *Transaction `protobuf:"bytes,3,opt,name=transaction,proto3" json:"transaction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommitDraftTransactionResponse) Reset() {
	*x = CommitDraftTransactionResponse{}
	mi := &file_rpc_dto_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommitDraftTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommitDraftTransactionResponse) ProtoMessage() {}

func (x *CommitDraftTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommitDraftTransactionResponse.ProtoReflect.Descriptor instead.
func (*CommitDraftTransactionResponse) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{3}
}

func (x *CommitDraftTransactionResponse) GetError() bool {
	if x != nil {
		return x.Error
	}
	return false
}

func (x *CommitDraftTransactionResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CommitDraftTransactionResponse) GetTransaction() *Transaction {
	if x != nil {
		return x.Transaction
	}
	return nil
}

// CommitTransactionRequest is the request object for commit transaction
type CommitTransactionRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// source_account is the source account address for payer of the payment
	SourceAccount string `protobuf:"bytes,1,opt,name=source_account,json=sourceAccount,proto3" json:"source_account,omitempty"`
	// destination_account is the destination account address for acceptor of the payment
	DestinationAccount string `protobuf:"bytes,2,opt,name=destination_account,json=destinationAccount,proto3" json:"destination_account,omitempty"`
	// card_number is the card number of the payer
	CardNumber string `protobuf:"bytes,3,opt,name=card_number,json=cardNumber,proto3" json:"card_number,omitempty"`
	// amount is the amount to be paid
	Amount float64 `protobuf:"fixed64,4,opt,name=amount,proto3" json:"amount,omitempty"`
	// asset is the asset type of the amount
	Asset string `protobuf:"bytes,5,opt,name=asset,proto3" json:"asset,omitempty"`
	// description is the description of the payment
	Description string `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	// reference_id is actually idempotency key for making create draft transaction idempotent
	ReferenceId string `protobuf:"bytes,7,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
	// metadata is the metadata of the transaction
	Metadata      map[string]string `protobuf:"bytes,8,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommitTransactionRequest) Reset() {
	*x = CommitTransactionRequest{}
	mi := &file_rpc_dto_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommitTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommitTransactionRequest) ProtoMessage() {}

func (x *CommitTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommitTransactionRequest.ProtoReflect.Descriptor instead.
func (*CommitTransactionRequest) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{4}
}

func (x *CommitTransactionRequest) GetSourceAccount() string {
	if x != nil {
		return x.SourceAccount
	}
	return ""
}

func (x *CommitTransactionRequest) GetDestinationAccount() string {
	if x != nil {
		return x.DestinationAccount
	}
	return ""
}

func (x *CommitTransactionRequest) GetCardNumber() string {
	if x != nil {
		return x.CardNumber
	}
	return ""
}

func (x *CommitTransactionRequest) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *CommitTransactionRequest) GetAsset() string {
	if x != nil {
		return x.Asset
	}
	return ""
}

func (x *CommitTransactionRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CommitTransactionRequest) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *CommitTransactionRequest) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// CommitTransactionResponse is the response object for commit transaction
type CommitTransactionResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// whether the request returns error or no.
	Error bool `protobuf:"varint,1,opt,name=error,proto3" json:"error,omitempty"`
	// error message if occurred or success message
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// transaction is the transaction object
	Transaction   *Transaction `protobuf:"bytes,3,opt,name=transaction,proto3" json:"transaction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommitTransactionResponse) Reset() {
	*x = CommitTransactionResponse{}
	mi := &file_rpc_dto_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommitTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommitTransactionResponse) ProtoMessage() {}

func (x *CommitTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommitTransactionResponse.ProtoReflect.Descriptor instead.
func (*CommitTransactionResponse) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{5}
}

func (x *CommitTransactionResponse) GetError() bool {
	if x != nil {
		return x.Error
	}
	return false
}

func (x *CommitTransactionResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CommitTransactionResponse) GetTransaction() *Transaction {
	if x != nil {
		return x.Transaction
	}
	return nil
}

// FetchUserCardsListForDraftTransactionResponse is the response object for fetch user cards for draft transaction
type FetchUserCardsListForDraftTransactionResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// whether the request returns error or no.
	Error bool `protobuf:"varint,1,opt,name=error,proto3" json:"error,omitempty"`
	// error message if occurred or success message
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// transaction is the transaction object
	CardNumbersList []string `protobuf:"bytes,3,rep,name=card_numbers_list,json=cardNumbersList,proto3" json:"card_numbers_list,omitempty"`
	// count of cards
	Count         int32 `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FetchUserCardsListForDraftTransactionResponse) Reset() {
	*x = FetchUserCardsListForDraftTransactionResponse{}
	mi := &file_rpc_dto_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FetchUserCardsListForDraftTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchUserCardsListForDraftTransactionResponse) ProtoMessage() {}

func (x *FetchUserCardsListForDraftTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchUserCardsListForDraftTransactionResponse.ProtoReflect.Descriptor instead.
func (*FetchUserCardsListForDraftTransactionResponse) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{6}
}

func (x *FetchUserCardsListForDraftTransactionResponse) GetError() bool {
	if x != nil {
		return x.Error
	}
	return false
}

func (x *FetchUserCardsListForDraftTransactionResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *FetchUserCardsListForDraftTransactionResponse) GetCardNumbersList() []string {
	if x != nil {
		return x.CardNumbersList
	}
	return nil
}

func (x *FetchUserCardsListForDraftTransactionResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// SendCardToCardTOTPCodeRequest is the request object for send card to card TOTP code
type SendCardToCardTOTPCodeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// source_card_number is the card number of the payer
	SourceCardNumber string `protobuf:"bytes,1,opt,name=source_card_number,json=sourceCardNumber,proto3" json:"source_card_number,omitempty"`
	// cvv2 of source card
	Cvv string `protobuf:"bytes,2,opt,name=cvv,proto3" json:"cvv,omitempty"`
	// source card expire year
	ExpireYear string `protobuf:"bytes,3,opt,name=expire_year,json=expireYear,proto3" json:"expire_year,omitempty"`
	// source card expire month
	ExpireMonth string `protobuf:"bytes,4,opt,name=expire_month,json=expireMonth,proto3" json:"expire_month,omitempty"`
	// amount of asset to be transfer
	Amount string `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
	// transaction reference idempotency key
	ReferenceId string `protobuf:"bytes,6,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
	// destination card number
	DestinationCardNumber string `protobuf:"bytes,7,opt,name=destination_card_number,json=destinationCardNumber,proto3" json:"destination_card_number,omitempty"`
	// asset type to be transfer
	Asset         string `protobuf:"bytes,8,opt,name=asset,proto3" json:"asset,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendCardToCardTOTPCodeRequest) Reset() {
	*x = SendCardToCardTOTPCodeRequest{}
	mi := &file_rpc_dto_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendCardToCardTOTPCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendCardToCardTOTPCodeRequest) ProtoMessage() {}

func (x *SendCardToCardTOTPCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendCardToCardTOTPCodeRequest.ProtoReflect.Descriptor instead.
func (*SendCardToCardTOTPCodeRequest) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{7}
}

func (x *SendCardToCardTOTPCodeRequest) GetSourceCardNumber() string {
	if x != nil {
		return x.SourceCardNumber
	}
	return ""
}

func (x *SendCardToCardTOTPCodeRequest) GetCvv() string {
	if x != nil {
		return x.Cvv
	}
	return ""
}

func (x *SendCardToCardTOTPCodeRequest) GetExpireYear() string {
	if x != nil {
		return x.ExpireYear
	}
	return ""
}

func (x *SendCardToCardTOTPCodeRequest) GetExpireMonth() string {
	if x != nil {
		return x.ExpireMonth
	}
	return ""
}

func (x *SendCardToCardTOTPCodeRequest) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *SendCardToCardTOTPCodeRequest) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *SendCardToCardTOTPCodeRequest) GetDestinationCardNumber() string {
	if x != nil {
		return x.DestinationCardNumber
	}
	return ""
}

func (x *SendCardToCardTOTPCodeRequest) GetAsset() string {
	if x != nil {
		return x.Asset
	}
	return ""
}

// SendCardToCardTOTPCodeResponse is the response object for send card to card TOTP code
type SendCardToCardTOTPCodeResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// whether the request returns error or no.
	Error bool `protobuf:"varint,1,opt,name=error,proto3" json:"error,omitempty"`
	// error message if occurred or success message
	Message       string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendCardToCardTOTPCodeResponse) Reset() {
	*x = SendCardToCardTOTPCodeResponse{}
	mi := &file_rpc_dto_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendCardToCardTOTPCodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendCardToCardTOTPCodeResponse) ProtoMessage() {}

func (x *SendCardToCardTOTPCodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendCardToCardTOTPCodeResponse.ProtoReflect.Descriptor instead.
func (*SendCardToCardTOTPCodeResponse) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{8}
}

func (x *SendCardToCardTOTPCodeResponse) GetError() bool {
	if x != nil {
		return x.Error
	}
	return false
}

func (x *SendCardToCardTOTPCodeResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// CommitCardToCardTransactionRequest is the request object for commit card to card transaction
type CommitCardToCardTransactionRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// request is the request object for send card to card TOTP code
	Request *SendCardToCardTOTPCodeRequest `protobuf:"bytes,1,opt,name=request,proto3" json:"request,omitempty"`
	// totp code
	Totp          string `protobuf:"bytes,2,opt,name=totp,proto3" json:"totp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommitCardToCardTransactionRequest) Reset() {
	*x = CommitCardToCardTransactionRequest{}
	mi := &file_rpc_dto_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommitCardToCardTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommitCardToCardTransactionRequest) ProtoMessage() {}

func (x *CommitCardToCardTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommitCardToCardTransactionRequest.ProtoReflect.Descriptor instead.
func (*CommitCardToCardTransactionRequest) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{9}
}

func (x *CommitCardToCardTransactionRequest) GetRequest() *SendCardToCardTOTPCodeRequest {
	if x != nil {
		return x.Request
	}
	return nil
}

func (x *CommitCardToCardTransactionRequest) GetTotp() string {
	if x != nil {
		return x.Totp
	}
	return ""
}

// Define an empty message for methods that require no input.
type Empty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Empty) Reset() {
	*x = Empty{}
	mi := &file_rpc_dto_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{10}
}

var File_rpc_dto_proto protoreflect.FileDescriptor

const file_rpc_dto_proto_rawDesc = "" +
	"\n" +
	"\rrpc_dto.proto\x12\x02pb\x1a\x1brpc_draft_transaction.proto\x1a\x15rpc_transaction.proto\"\xac\x03\n" +
	"\x17DraftTransactionRequest\x12)\n" +
	"\x10payer_identifier\x18\x01 \x01(\tR\x0fpayerIdentifier\x12\x1d\n" +
	"\n" +
	"draft_type\x18\x02 \x01(\tR\tdraftType\x12/\n" +
	"\x13destination_account\x18\x03 \x01(\tR\x12destinationAccount\x12\x1f\n" +
	"\vcard_number\x18\x04 \x01(\tR\n" +
	"cardNumber\x12\x16\n" +
	"\x06amount\x18\x05 \x01(\tR\x06amount\x12\x14\n" +
	"\x05asset\x18\x06 \x01(\tR\x05asset\x12 \n" +
	"\vdescription\x18\a \x01(\tR\vdescription\x12!\n" +
	"\freference_id\x18\b \x01(\tR\vreferenceId\x12E\n" +
	"\bmetadata\x18\t \x03(\v2).pb.DraftTransactionRequest.MetadataEntryR\bmetadata\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x8d\x01\n" +
	"\x18DraftTransactionResponse\x12\x14\n" +
	"\x05error\x18\x01 \x01(\bR\x05error\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12A\n" +
	"\x11draft_transaction\x18\x03 \x01(\v2\x14.pb.DraftTransactionR\x10draftTransaction\"\xb2\x01\n" +
	"\x1dCommitDraftTransactionRequest\x128\n" +
	"\x18payment_draft_identifier\x18\x01 \x01(\tR\x16paymentDraftIdentifier\x12!\n" +
	"\freference_id\x18\x02 \x01(\tR\vreferenceId\x12\x12\n" +
	"\x04totp\x18\x03 \x01(\tR\x04totp\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\"\x83\x01\n" +
	"\x1eCommitDraftTransactionResponse\x12\x14\n" +
	"\x05error\x18\x01 \x01(\bR\x05error\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x121\n" +
	"\vtransaction\x18\x03 \x01(\v2\x0f.pb.TransactionR\vtransaction\"\x8b\x03\n" +
	"\x18CommitTransactionRequest\x12%\n" +
	"\x0esource_account\x18\x01 \x01(\tR\rsourceAccount\x12/\n" +
	"\x13destination_account\x18\x02 \x01(\tR\x12destinationAccount\x12\x1f\n" +
	"\vcard_number\x18\x03 \x01(\tR\n" +
	"cardNumber\x12\x16\n" +
	"\x06amount\x18\x04 \x01(\x01R\x06amount\x12\x14\n" +
	"\x05asset\x18\x05 \x01(\tR\x05asset\x12 \n" +
	"\vdescription\x18\x06 \x01(\tR\vdescription\x12!\n" +
	"\freference_id\x18\a \x01(\tR\vreferenceId\x12F\n" +
	"\bmetadata\x18\b \x03(\v2*.pb.CommitTransactionRequest.MetadataEntryR\bmetadata\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"~\n" +
	"\x19CommitTransactionResponse\x12\x14\n" +
	"\x05error\x18\x01 \x01(\bR\x05error\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x121\n" +
	"\vtransaction\x18\x03 \x01(\v2\x0f.pb.TransactionR\vtransaction\"\xa1\x01\n" +
	"-FetchUserCardsListForDraftTransactionResponse\x12\x14\n" +
	"\x05error\x18\x01 \x01(\bR\x05error\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12*\n" +
	"\x11card_numbers_list\x18\x03 \x03(\tR\x0fcardNumbersList\x12\x14\n" +
	"\x05count\x18\x04 \x01(\x05R\x05count\"\xac\x02\n" +
	"\x1dSendCardToCardTOTPCodeRequest\x12,\n" +
	"\x12source_card_number\x18\x01 \x01(\tR\x10sourceCardNumber\x12\x10\n" +
	"\x03cvv\x18\x02 \x01(\tR\x03cvv\x12\x1f\n" +
	"\vexpire_year\x18\x03 \x01(\tR\n" +
	"expireYear\x12!\n" +
	"\fexpire_month\x18\x04 \x01(\tR\vexpireMonth\x12\x16\n" +
	"\x06amount\x18\x05 \x01(\tR\x06amount\x12!\n" +
	"\freference_id\x18\x06 \x01(\tR\vreferenceId\x126\n" +
	"\x17destination_card_number\x18\a \x01(\tR\x15destinationCardNumber\x12\x14\n" +
	"\x05asset\x18\b \x01(\tR\x05asset\"P\n" +
	"\x1eSendCardToCardTOTPCodeResponse\x12\x14\n" +
	"\x05error\x18\x01 \x01(\bR\x05error\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"u\n" +
	"\"CommitCardToCardTransactionRequest\x12;\n" +
	"\arequest\x18\x01 \x01(\v2!.pb.SendCardToCardTOTPCodeRequestR\arequest\x12\x12\n" +
	"\x04totp\x18\x02 \x01(\tR\x04totp\"\a\n" +
	"\x05EmptyB,Z*github.com/liveutil/transaction_service/pbb\x06proto3"

var (
	file_rpc_dto_proto_rawDescOnce sync.Once
	file_rpc_dto_proto_rawDescData []byte
)

func file_rpc_dto_proto_rawDescGZIP() []byte {
	file_rpc_dto_proto_rawDescOnce.Do(func() {
		file_rpc_dto_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_dto_proto_rawDesc), len(file_rpc_dto_proto_rawDesc)))
	})
	return file_rpc_dto_proto_rawDescData
}

var file_rpc_dto_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_rpc_dto_proto_goTypes = []any{
	(*DraftTransactionRequest)(nil),                       // 0: pb.DraftTransactionRequest
	(*DraftTransactionResponse)(nil),                      // 1: pb.DraftTransactionResponse
	(*CommitDraftTransactionRequest)(nil),                 // 2: pb.CommitDraftTransactionRequest
	(*CommitDraftTransactionResponse)(nil),                // 3: pb.CommitDraftTransactionResponse
	(*CommitTransactionRequest)(nil),                      // 4: pb.CommitTransactionRequest
	(*CommitTransactionResponse)(nil),                     // 5: pb.CommitTransactionResponse
	(*FetchUserCardsListForDraftTransactionResponse)(nil), // 6: pb.FetchUserCardsListForDraftTransactionResponse
	(*SendCardToCardTOTPCodeRequest)(nil),                 // 7: pb.SendCardToCardTOTPCodeRequest
	(*SendCardToCardTOTPCodeResponse)(nil),                // 8: pb.SendCardToCardTOTPCodeResponse
	(*CommitCardToCardTransactionRequest)(nil),            // 9: pb.CommitCardToCardTransactionRequest
	(*Empty)(nil),            // 10: pb.Empty
	nil,                      // 11: pb.DraftTransactionRequest.MetadataEntry
	nil,                      // 12: pb.CommitTransactionRequest.MetadataEntry
	(*DraftTransaction)(nil), // 13: pb.DraftTransaction
	(*Transaction)(nil),      // 14: pb.Transaction
}
var file_rpc_dto_proto_depIdxs = []int32{
	11, // 0: pb.DraftTransactionRequest.metadata:type_name -> pb.DraftTransactionRequest.MetadataEntry
	13, // 1: pb.DraftTransactionResponse.draft_transaction:type_name -> pb.DraftTransaction
	14, // 2: pb.CommitDraftTransactionResponse.transaction:type_name -> pb.Transaction
	12, // 3: pb.CommitTransactionRequest.metadata:type_name -> pb.CommitTransactionRequest.MetadataEntry
	14, // 4: pb.CommitTransactionResponse.transaction:type_name -> pb.Transaction
	7,  // 5: pb.CommitCardToCardTransactionRequest.request:type_name -> pb.SendCardToCardTOTPCodeRequest
	6,  // [6:6] is the sub-list for method output_type
	6,  // [6:6] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_rpc_dto_proto_init() }
func file_rpc_dto_proto_init() {
	if File_rpc_dto_proto != nil {
		return
	}
	file_rpc_draft_transaction_proto_init()
	file_rpc_transaction_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_dto_proto_rawDesc), len(file_rpc_dto_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rpc_dto_proto_goTypes,
		DependencyIndexes: file_rpc_dto_proto_depIdxs,
		MessageInfos:      file_rpc_dto_proto_msgTypes,
	}.Build()
	File_rpc_dto_proto = out.File
	file_rpc_dto_proto_goTypes = nil
	file_rpc_dto_proto_depIdxs = nil
}
