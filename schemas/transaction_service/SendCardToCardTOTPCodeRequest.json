{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "SendCardToCardTOTPCodeRequest", "$protected": true, "title": "SendCardToCardTOTPCodeRequest", "description": "Send Card To Card TOTP Code Request", "type": "object", "required": ["amount", "asset", "source_card_number", "destination_card_number", "reference_id", "expire_month", "expire_year", "cvv"], "properties": {"amount": {"type": "string"}, "asset": {"type": "string"}, "source_card_number": {"type": "string"}, "destination_card_number": {"type": "string"}, "expire_month": {"type": "string"}, "expire_year": {"type": "string"}, "cvv": {"type": "string"}, "reference_id": {"type": "string"}}}