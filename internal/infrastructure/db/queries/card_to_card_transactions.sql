-- name: UpsertCardToCardTransaction :one
INSERT INTO card_to_card_transaction (
        identifier,
        totp,
        reference_id,
        meta_data,
        source_card_number,
        destination_card_number,
        asset,
        amount,
        expires_at,
        updated_at
    )
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, 'now()') ON CONFLICT (identifier) DO
UPDATE
SET totp = $2,
    reference_id = $3,
    meta_data = $4,
    source_card_number = $5,
    destination_card_number = $6,
    asset = $7,
    amount = $8,
    expires_at = $9,
    updated_at = 'now()'
RETURNING *;

-- name: SoftDeleteCardToCardTransaction :exec
UPDATE card_to_card_transaction
SET deleted_at = 'now()'
WHERE identifier = $1;

-- name: GetCardToCardTransactionByReferenceID :one
SELECT *
FROM card_to_card_transaction
WHERE reference_id = $1
    AND expires_at > 'now()'
    AND deleted_at IS NULL
LIMIT 1;