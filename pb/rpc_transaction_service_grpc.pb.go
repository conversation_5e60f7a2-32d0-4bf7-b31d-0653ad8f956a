// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: rpc_transaction_service.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TransactionService_DraftTransaction_FullMethodName                  = "/pb.TransactionService/DraftTransaction"
	TransactionService_CommitDraftTransaction_FullMethodName            = "/pb.TransactionService/CommitDraftTransaction"
	TransactionService_CommitTransaction_FullMethodName                 = "/pb.TransactionService/CommitTransaction"
	TransactionService_FetchUserCardsForDraftTransaction_FullMethodName = "/pb.TransactionService/FetchUserCardsForDraftTransaction"
	TransactionService_SendCardToCardTOTP_FullMethodName                = "/pb.TransactionService/SendCardToCardTOTP"
	TransactionService_CommitCardToCardTransaction_FullMethodName       = "/pb.TransactionService/CommitCardToCardTransaction"
)

// TransactionServiceClient is the client API for TransactionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Transaction Service API for processing transaction request and related operations.
type TransactionServiceClient interface {
	// DraftTransaction process request for storing temporary transaction with specified customer mobile/card no or national id
	DraftTransaction(ctx context.Context, in *DraftTransactionRequest, opts ...grpc.CallOption) (*DraftTransactionResponse, error)
	// CommitDraftTransaction validate TOTP code that sent to customer's mobile number and commit transaction to Core Payment Service
	CommitDraftTransaction(ctx context.Context, in *CommitDraftTransactionRequest, opts ...grpc.CallOption) (*CommitDraftTransactionResponse, error)
	// CommitTransaction commit transaction to Core Payment Service
	CommitTransaction(ctx context.Context, in *CommitTransactionRequest, opts ...grpc.CallOption) (*CommitTransactionResponse, error)
	// FetchUserCards fetch user cards for letting him to chose one for transaction
	FetchUserCardsForDraftTransaction(ctx context.Context, in *DraftTransactionRequest, opts ...grpc.CallOption) (*FetchUserCardsListForDraftTransactionResponse, error)
	SendCardToCardTOTP(ctx context.Context, in *SendCardToCardTOTPCodeRequest, opts ...grpc.CallOption) (*SendCardToCardTOTPCodeResponse, error)
	CommitCardToCardTransaction(ctx context.Context, in *CommitCardToCardTransactionRequest, opts ...grpc.CallOption) (*CommitTransactionResponse, error)
}

type transactionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTransactionServiceClient(cc grpc.ClientConnInterface) TransactionServiceClient {
	return &transactionServiceClient{cc}
}

func (c *transactionServiceClient) DraftTransaction(ctx context.Context, in *DraftTransactionRequest, opts ...grpc.CallOption) (*DraftTransactionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DraftTransactionResponse)
	err := c.cc.Invoke(ctx, TransactionService_DraftTransaction_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionServiceClient) CommitDraftTransaction(ctx context.Context, in *CommitDraftTransactionRequest, opts ...grpc.CallOption) (*CommitDraftTransactionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommitDraftTransactionResponse)
	err := c.cc.Invoke(ctx, TransactionService_CommitDraftTransaction_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionServiceClient) CommitTransaction(ctx context.Context, in *CommitTransactionRequest, opts ...grpc.CallOption) (*CommitTransactionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommitTransactionResponse)
	err := c.cc.Invoke(ctx, TransactionService_CommitTransaction_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionServiceClient) FetchUserCardsForDraftTransaction(ctx context.Context, in *DraftTransactionRequest, opts ...grpc.CallOption) (*FetchUserCardsListForDraftTransactionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FetchUserCardsListForDraftTransactionResponse)
	err := c.cc.Invoke(ctx, TransactionService_FetchUserCardsForDraftTransaction_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionServiceClient) SendCardToCardTOTP(ctx context.Context, in *SendCardToCardTOTPCodeRequest, opts ...grpc.CallOption) (*SendCardToCardTOTPCodeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendCardToCardTOTPCodeResponse)
	err := c.cc.Invoke(ctx, TransactionService_SendCardToCardTOTP_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionServiceClient) CommitCardToCardTransaction(ctx context.Context, in *CommitCardToCardTransactionRequest, opts ...grpc.CallOption) (*CommitTransactionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CommitTransactionResponse)
	err := c.cc.Invoke(ctx, TransactionService_CommitCardToCardTransaction_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TransactionServiceServer is the server API for TransactionService service.
// All implementations must embed UnimplementedTransactionServiceServer
// for forward compatibility.
//
// Transaction Service API for processing transaction request and related operations.
type TransactionServiceServer interface {
	// DraftTransaction process request for storing temporary transaction with specified customer mobile/card no or national id
	DraftTransaction(context.Context, *DraftTransactionRequest) (*DraftTransactionResponse, error)
	// CommitDraftTransaction validate TOTP code that sent to customer's mobile number and commit transaction to Core Payment Service
	CommitDraftTransaction(context.Context, *CommitDraftTransactionRequest) (*CommitDraftTransactionResponse, error)
	// CommitTransaction commit transaction to Core Payment Service
	CommitTransaction(context.Context, *CommitTransactionRequest) (*CommitTransactionResponse, error)
	// FetchUserCards fetch user cards for letting him to chose one for transaction
	FetchUserCardsForDraftTransaction(context.Context, *DraftTransactionRequest) (*FetchUserCardsListForDraftTransactionResponse, error)
	SendCardToCardTOTP(context.Context, *SendCardToCardTOTPCodeRequest) (*SendCardToCardTOTPCodeResponse, error)
	CommitCardToCardTransaction(context.Context, *CommitCardToCardTransactionRequest) (*CommitTransactionResponse, error)
	mustEmbedUnimplementedTransactionServiceServer()
}

// UnimplementedTransactionServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTransactionServiceServer struct{}

func (UnimplementedTransactionServiceServer) DraftTransaction(context.Context, *DraftTransactionRequest) (*DraftTransactionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DraftTransaction not implemented")
}
func (UnimplementedTransactionServiceServer) CommitDraftTransaction(context.Context, *CommitDraftTransactionRequest) (*CommitDraftTransactionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CommitDraftTransaction not implemented")
}
func (UnimplementedTransactionServiceServer) CommitTransaction(context.Context, *CommitTransactionRequest) (*CommitTransactionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CommitTransaction not implemented")
}
func (UnimplementedTransactionServiceServer) FetchUserCardsForDraftTransaction(context.Context, *DraftTransactionRequest) (*FetchUserCardsListForDraftTransactionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchUserCardsForDraftTransaction not implemented")
}
func (UnimplementedTransactionServiceServer) SendCardToCardTOTP(context.Context, *SendCardToCardTOTPCodeRequest) (*SendCardToCardTOTPCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendCardToCardTOTP not implemented")
}
func (UnimplementedTransactionServiceServer) CommitCardToCardTransaction(context.Context, *CommitCardToCardTransactionRequest) (*CommitTransactionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CommitCardToCardTransaction not implemented")
}
func (UnimplementedTransactionServiceServer) mustEmbedUnimplementedTransactionServiceServer() {}
func (UnimplementedTransactionServiceServer) testEmbeddedByValue()                            {}

// UnsafeTransactionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TransactionServiceServer will
// result in compilation errors.
type UnsafeTransactionServiceServer interface {
	mustEmbedUnimplementedTransactionServiceServer()
}

func RegisterTransactionServiceServer(s grpc.ServiceRegistrar, srv TransactionServiceServer) {
	// If the following call pancis, it indicates UnimplementedTransactionServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TransactionService_ServiceDesc, srv)
}

func _TransactionService_DraftTransaction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DraftTransactionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServiceServer).DraftTransaction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TransactionService_DraftTransaction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServiceServer).DraftTransaction(ctx, req.(*DraftTransactionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TransactionService_CommitDraftTransaction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommitDraftTransactionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServiceServer).CommitDraftTransaction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TransactionService_CommitDraftTransaction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServiceServer).CommitDraftTransaction(ctx, req.(*CommitDraftTransactionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TransactionService_CommitTransaction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommitTransactionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServiceServer).CommitTransaction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TransactionService_CommitTransaction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServiceServer).CommitTransaction(ctx, req.(*CommitTransactionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TransactionService_FetchUserCardsForDraftTransaction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DraftTransactionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServiceServer).FetchUserCardsForDraftTransaction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TransactionService_FetchUserCardsForDraftTransaction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServiceServer).FetchUserCardsForDraftTransaction(ctx, req.(*DraftTransactionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TransactionService_SendCardToCardTOTP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendCardToCardTOTPCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServiceServer).SendCardToCardTOTP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TransactionService_SendCardToCardTOTP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServiceServer).SendCardToCardTOTP(ctx, req.(*SendCardToCardTOTPCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TransactionService_CommitCardToCardTransaction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommitCardToCardTransactionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServiceServer).CommitCardToCardTransaction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TransactionService_CommitCardToCardTransaction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServiceServer).CommitCardToCardTransaction(ctx, req.(*CommitCardToCardTransactionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TransactionService_ServiceDesc is the grpc.ServiceDesc for TransactionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TransactionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.TransactionService",
	HandlerType: (*TransactionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DraftTransaction",
			Handler:    _TransactionService_DraftTransaction_Handler,
		},
		{
			MethodName: "CommitDraftTransaction",
			Handler:    _TransactionService_CommitDraftTransaction_Handler,
		},
		{
			MethodName: "CommitTransaction",
			Handler:    _TransactionService_CommitTransaction_Handler,
		},
		{
			MethodName: "FetchUserCardsForDraftTransaction",
			Handler:    _TransactionService_FetchUserCardsForDraftTransaction_Handler,
		},
		{
			MethodName: "SendCardToCardTOTP",
			Handler:    _TransactionService_SendCardToCardTOTP_Handler,
		},
		{
			MethodName: "CommitCardToCardTransaction",
			Handler:    _TransactionService_CommitCardToCardTransaction_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rpc_transaction_service.proto",
}
