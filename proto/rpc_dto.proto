syntax = "proto3";

package pb;

import "rpc_draft_transaction.proto";
import "rpc_transaction.proto";

option go_package = "github.com/liveutil/transaction_service/pb";

// DraftTransactionRequest is the request object for draft transaction
message DraftTransactionRequest {
  // payer_identifier is the mobile / national id / card no of the payer
  string payer_identifier = 1;
  // draft_type is the type of draft transaction
  string draft_type = 2;
  // destination_account is the destination account address for acceptor of the payment
  string destination_account = 3;
  // card_number is the card number of the payer
  string card_number = 4;
  // amount is the amount to be paid
  string amount = 5;
  // asset is the asset type of the amount
  string asset = 6;
  // description is the description of the payment
  string description = 7;
  // reference_id is actually idempotency key for making create draft transaction idempotent
  string reference_id = 8;
  // metadata is the metadata of the transaction
  map<string, string> metadata = 9;
}

// DraftTransactionResponse is the response object for draft transaction
message DraftTransactionResponse {
  // whether the request returns error or no.
  bool error = 1;
  // error message if occurred or success message
  string message = 2;
  // payment draft id
  DraftTransaction draft_transaction = 3;
}

// CommitDraftTransactionRequest is the request object for commit draft transaction
message CommitDraftTransactionRequest {
  // payment draft id
  string payment_draft_identifier = 1;
  // reference_id is actually idempotency key for making create draft transaction idempotent
  string reference_id = 2;
  // totp code
  string totp = 3;
  // description is the description of the payment
  string description = 4;
}

// CommitDraftTransactionResponse is the response object for commit draft transaction
message CommitDraftTransactionResponse {
  // whether the request returns error or no.
  bool error = 1;
  // error message if occurred or success message
  string message = 2;
  // transaction is the transaction object
  Transaction transaction = 3;
}

// CommitTransactionRequest is the request object for commit transaction
message CommitTransactionRequest {
  // source_account is the source account address for payer of the payment
  string source_account = 1;
  // destination_account is the destination account address for acceptor of the payment
  string destination_account = 2;
  // card_number is the card number of the payer
  string card_number = 3;
  // amount is the amount to be paid
  double amount = 4;
  // asset is the asset type of the amount
  string asset = 5;
  // description is the description of the payment
  string description = 6;
  // reference_id is actually idempotency key for making create draft transaction idempotent
  string reference_id = 7;
  // metadata is the metadata of the transaction
  map<string, string> metadata = 8;
}

// CommitTransactionResponse is the response object for commit transaction
message CommitTransactionResponse {
  // whether the request returns error or no.
  bool error = 1;
  // error message if occurred or success message
  string message = 2;
  // transaction is the transaction object
  Transaction transaction = 3;
}

// FetchUserCardsListForDraftTransactionResponse is the response object for fetch user cards for draft transaction
message FetchUserCardsListForDraftTransactionResponse {
  // whether the request returns error or no.
  bool error = 1;
  // error message if occurred or success message
  string message = 2;
  // transaction is the transaction object
  repeated string card_numbers_list = 3;
  // count of cards
  int32 count = 4;
}

// SendCardToCardTOTPCodeRequest is the request object for send card to card TOTP code
message SendCardToCardTOTPCodeRequest {
  // source_card_number is the card number of the payer
  string source_card_number = 1;
  // cvv2 of source card
  string cvv = 2;
  // source card expire year
  string expire_year = 3;
  // source card expire month
  string expire_month = 4;
  // amount of asset to be transfer
  string amount = 5;
  // transaction reference idempotency key
  string reference_id = 6;
  // destination card number
  string destination_card_number = 7;
  // asset type to be transfer
  string asset = 8;
}

// SendCardToCardTOTPCodeResponse is the response object for send card to card TOTP code
message SendCardToCardTOTPCodeResponse {
  // whether the request returns error or no.
  bool error = 1;
  // error message if occurred or success message
  string message = 2;
}

// CommitCardToCardTransactionRequest is the request object for commit card to card transaction
message CommitCardToCardTransactionRequest {
  // request is the request object for send card to card TOTP code
  SendCardToCardTOTPCodeRequest request = 1;
  // totp code
  string totp = 2;
}

// Define an empty message for methods that require no input.
message Empty {}