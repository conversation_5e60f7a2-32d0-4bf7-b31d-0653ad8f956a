// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: transaction_drafts.sql

package postgres

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const getDraftTransactionByIdentifier = `-- name: GetDraftTransactionByIdentifier :one
SELECT id, identifier, draft_type, totp, reference_id, meta_data, card_number, source_account, destination_account, asset, amount, expires_at, updated_at, created_at, deleted_at
FROM transaction_drafts
WHERE identifier = $1
    AND expires_at > 'now()'
LIMIT 1
`

func (q *Queries) GetDraftTransactionByIdentifier(ctx context.Context, identifier string) (TransactionDraft, error) {
	row := q.db.QueryRow(ctx, getDraftTransactionByIdentifier, identifier)
	var i TransactionDraft
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.DraftType,
		&i.Totp,
		&i.ReferenceID,
		&i.MetaData,
		&i.CardNumber,
		&i.SourceAccount,
		&i.DestinationAccount,
		&i.Asset,
		&i.Amount,
		&i.ExpiresAt,
		&i.UpdatedAt,
		&i.CreatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getDraftTransactionByIdentifierAndReference = `-- name: GetDraftTransactionByIdentifierAndReference :one
SELECT id, identifier, draft_type, totp, reference_id, meta_data, card_number, source_account, destination_account, asset, amount, expires_at, updated_at, created_at, deleted_at
FROM transaction_drafts
WHERE identifier = $1
    AND reference_id = $2
    AND expires_at > 'now()'
LIMIT 1
`

type GetDraftTransactionByIdentifierAndReferenceParams struct {
	Identifier  string `json:"identifier"`
	ReferenceID string `json:"reference_id"`
}

func (q *Queries) GetDraftTransactionByIdentifierAndReference(ctx context.Context, arg GetDraftTransactionByIdentifierAndReferenceParams) (TransactionDraft, error) {
	row := q.db.QueryRow(ctx, getDraftTransactionByIdentifierAndReference, arg.Identifier, arg.ReferenceID)
	var i TransactionDraft
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.DraftType,
		&i.Totp,
		&i.ReferenceID,
		&i.MetaData,
		&i.CardNumber,
		&i.SourceAccount,
		&i.DestinationAccount,
		&i.Asset,
		&i.Amount,
		&i.ExpiresAt,
		&i.UpdatedAt,
		&i.CreatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const softDeleteDraftTransaction = `-- name: SoftDeleteDraftTransaction :exec
UPDATE transaction_drafts
SET deleted_at = 'now()'
WHERE identifier = $1
`

func (q *Queries) SoftDeleteDraftTransaction(ctx context.Context, identifier string) error {
	_, err := q.db.Exec(ctx, softDeleteDraftTransaction, identifier)
	return err
}

const upsertTransaction = `-- name: UpsertTransaction :one
INSERT INTO transaction_drafts (
        identifier,
        draft_type,
        totp,
        reference_id,
        card_number,
        source_account,
        destination_account,
        asset,
        amount,
        meta_data,
        expires_at,
        updated_at
    )
VALUES (
        $1,
        $2,
        $3,
        $4,
        $5,
        $6,
        $7,
        $8,
        $9,
        $10,
        $11,
        'now()'
    )
RETURNING id, identifier, draft_type, totp, reference_id, meta_data, card_number, source_account, destination_account, asset, amount, expires_at, updated_at, created_at, deleted_at
`

type UpsertTransactionParams struct {
	Identifier         string               `json:"identifier"`
	DraftType          TransactionDraftType `json:"draft_type"`
	Totp               string               `json:"totp"`
	ReferenceID        string               `json:"reference_id"`
	CardNumber         string               `json:"card_number"`
	SourceAccount      string               `json:"source_account"`
	DestinationAccount string               `json:"destination_account"`
	Asset              string               `json:"asset"`
	Amount             float64              `json:"amount"`
	MetaData           []byte               `json:"meta_data"`
	ExpiresAt          pgtype.Timestamptz   `json:"expires_at"`
}

func (q *Queries) UpsertTransaction(ctx context.Context, arg UpsertTransactionParams) (TransactionDraft, error) {
	row := q.db.QueryRow(ctx, upsertTransaction,
		arg.Identifier,
		arg.DraftType,
		arg.Totp,
		arg.ReferenceID,
		arg.CardNumber,
		arg.SourceAccount,
		arg.DestinationAccount,
		arg.Asset,
		arg.Amount,
		arg.MetaData,
		arg.ExpiresAt,
	)
	var i TransactionDraft
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.DraftType,
		&i.Totp,
		&i.ReferenceID,
		&i.MetaData,
		&i.CardNumber,
		&i.SourceAccount,
		&i.DestinationAccount,
		&i.Asset,
		&i.Amount,
		&i.ExpiresAt,
		&i.UpdatedAt,
		&i.CreatedAt,
		&i.DeletedAt,
	)
	return i, err
}
