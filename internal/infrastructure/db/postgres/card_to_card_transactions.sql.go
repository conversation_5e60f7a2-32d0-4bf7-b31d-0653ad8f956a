// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: card_to_card_transactions.sql

package postgres

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const getCardToCardTransactionByReferenceID = `-- name: GetCardToCardTransactionByReferenceID :one
SELECT id, identifier, totp, reference_id, meta_data, source_card_number, destination_card_number, asset, amount, expires_at, updated_at, created_at, deleted_at
FROM card_to_card_transaction
WHERE reference_id = $1
    AND expires_at > 'now()'
    AND deleted_at IS NULL
LIMIT 1
`

func (q *Queries) GetCardToCardTransactionByReferenceID(ctx context.Context, referenceID string) (CardToCardTransaction, error) {
	row := q.db.QueryRow(ctx, getCardToCardTransactionByReferenceID, referenceID)
	var i CardToCardTransaction
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Totp,
		&i.ReferenceID,
		&i.MetaData,
		&i.SourceCardNumber,
		&i.DestinationCardNumber,
		&i.Asset,
		&i.Amount,
		&i.ExpiresAt,
		&i.UpdatedAt,
		&i.CreatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const softDeleteCardToCardTransaction = `-- name: SoftDeleteCardToCardTransaction :exec
UPDATE card_to_card_transaction
SET deleted_at = 'now()'
WHERE identifier = $1
`

func (q *Queries) SoftDeleteCardToCardTransaction(ctx context.Context, identifier string) error {
	_, err := q.db.Exec(ctx, softDeleteCardToCardTransaction, identifier)
	return err
}

const upsertCardToCardTransaction = `-- name: UpsertCardToCardTransaction :one
INSERT INTO card_to_card_transaction (
        identifier,
        totp,
        reference_id,
        meta_data,
        source_card_number,
        destination_card_number,
        asset,
        amount,
        expires_at,
        updated_at
    )
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, 'now()') ON CONFLICT (identifier) DO
UPDATE
SET totp = $2,
    reference_id = $3,
    meta_data = $4,
    source_card_number = $5,
    destination_card_number = $6,
    asset = $7,
    amount = $8,
    expires_at = $9,
    updated_at = 'now()'
RETURNING id, identifier, totp, reference_id, meta_data, source_card_number, destination_card_number, asset, amount, expires_at, updated_at, created_at, deleted_at
`

type UpsertCardToCardTransactionParams struct {
	Identifier            string             `json:"identifier"`
	Totp                  string             `json:"totp"`
	ReferenceID           string             `json:"reference_id"`
	MetaData              []byte             `json:"meta_data"`
	SourceCardNumber      string             `json:"source_card_number"`
	DestinationCardNumber string             `json:"destination_card_number"`
	Asset                 string             `json:"asset"`
	Amount                float64            `json:"amount"`
	ExpiresAt             pgtype.Timestamptz `json:"expires_at"`
}

func (q *Queries) UpsertCardToCardTransaction(ctx context.Context, arg UpsertCardToCardTransactionParams) (CardToCardTransaction, error) {
	row := q.db.QueryRow(ctx, upsertCardToCardTransaction,
		arg.Identifier,
		arg.Totp,
		arg.ReferenceID,
		arg.MetaData,
		arg.SourceCardNumber,
		arg.DestinationCardNumber,
		arg.Asset,
		arg.Amount,
		arg.ExpiresAt,
	)
	var i CardToCardTransaction
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Totp,
		&i.ReferenceID,
		&i.MetaData,
		&i.SourceCardNumber,
		&i.DestinationCardNumber,
		&i.Asset,
		&i.Amount,
		&i.ExpiresAt,
		&i.UpdatedAt,
		&i.CreatedAt,
		&i.DeletedAt,
	)
	return i, err
}
