// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package postgres

import (
	"database/sql/driver"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

type TransactionDraftType string

const (
	TransactionDraftTypeMOBILE     TransactionDraftType = "MOBILE"
	TransactionDraftTypeNATIONALID TransactionDraftType = "NATIONAL_ID"
	TransactionDraftTypeCARDNUMBER TransactionDraftType = "CARD_NUMBER"
	TransactionDraftTypeCARDTOCARD TransactionDraftType = "CARD_TO_CARD"
)

func (e *TransactionDraftType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = TransactionDraftType(s)
	case string:
		*e = TransactionDraftType(s)
	default:
		return fmt.Errorf("unsupported scan type for TransactionDraftType: %T", src)
	}
	return nil
}

type NullTransactionDraftType struct {
	TransactionDraftType TransactionDraftType `json:"transaction_draft_type"`
	Valid                bool                 `json:"valid"` // Valid is true if TransactionDraftType is not NULL
}

// <PERSON><PERSON> implements the Scanner interface.
func (ns *NullTransactionDraftType) Scan(value interface{}) error {
	if value == nil {
		ns.TransactionDraftType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.TransactionDraftType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullTransactionDraftType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.TransactionDraftType), nil
}

type WorkflowType string

const (
	WorkflowTypeNUMSCRIPT    WorkflowType = "NUMSCRIPT"
	WorkflowTypeWORKFLOWYAML WorkflowType = "WORKFLOW_YAML"
)

func (e *WorkflowType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = WorkflowType(s)
	case string:
		*e = WorkflowType(s)
	default:
		return fmt.Errorf("unsupported scan type for WorkflowType: %T", src)
	}
	return nil
}

type NullWorkflowType struct {
	WorkflowType WorkflowType `json:"workflow_type"`
	Valid        bool         `json:"valid"` // Valid is true if WorkflowType is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullWorkflowType) Scan(value interface{}) error {
	if value == nil {
		ns.WorkflowType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.WorkflowType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullWorkflowType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.WorkflowType), nil
}

type CardToCardTransaction struct {
	// card to card transaction internal system unique id
	ID int64 `json:"id"`
	// unique external identifier for internal-external system identifier isolation
	Identifier string `json:"identifier"`
	// bcrypted TOTP code for validating transaction during committing it
	Totp string `json:"totp"`
	// idempotency key for making create card to card transaction transaction idempotent
	ReferenceID string `json:"reference_id"`
	// transaction metadatas
	MetaData []byte `json:"meta_data"`
	// source card number to send asset
	SourceCardNumber string `json:"source_card_number"`
	// destination card number to send asset
	DestinationCardNumber string `json:"destination_card_number"`
	// asset for transaction flow to match with
	Asset string `json:"asset"`
	// amount for transaction flow to match with
	Amount float64 `json:"amount"`
	// when the card to card transaction will expires
	ExpiresAt pgtype.Timestamptz `json:"expires_at"`
	// when card to card transaction was updated
	UpdatedAt time.Time `json:"updated_at"`
	// when card to card transaction was created
	CreatedAt time.Time `json:"created_at"`
	// when card to card transaction was deleted
	DeletedAt pgtype.Timestamptz `json:"deleted_at"`
}

type TransactionDraft struct {
	// draft internal system unique id
	ID int64 `json:"id"`
	// unique external identifier for internal-external system identifier isolation
	Identifier string `json:"identifier"`
	// represent type of draft transaction
	DraftType TransactionDraftType `json:"draft_type"`
	// bcrypted TOTP code for validating transaction during committing it
	Totp string `json:"totp"`
	// idempotency key for making create draft transaction idempotent
	ReferenceID string `json:"reference_id"`
	// transaction metadatas
	MetaData []byte `json:"meta_data"`
	// card number for card number draft type
	CardNumber string `json:"card_number"`
	// source account address for payer of the payment
	SourceAccount string `json:"source_account"`
	// destination account address for acceptor of the payment
	DestinationAccount string `json:"destination_account"`
	// asset for transaction flow to match with
	Asset string `json:"asset"`
	// amount for transaction flow to match with
	Amount float64 `json:"amount"`
	// when the draft will expires
	ExpiresAt pgtype.Timestamptz `json:"expires_at"`
	// when draft was updated
	UpdatedAt time.Time `json:"updated_at"`
	// when draft was created
	CreatedAt time.Time `json:"created_at"`
	// when draft was deleted
	DeletedAt pgtype.Timestamptz `json:"deleted_at"`
}

type Workflow struct {
	// transaction flow internal system unique id
	ID int64 `json:"id"`
	// unique external identifier for internal-external system identifier isolation
	Identifier string `json:"identifier"`
	// unique slug for transaction flow
	Slug string `json:"slug"`
	// description for transaction flow
	Description string `json:"description"`
	// is transaction flow active
	IsActive bool `json:"is_active"`
	// allow or disallow transaction route
	Allowed bool `json:"allowed"`
	// formance workflow id for transaction flow
	WorkflowID string `json:"workflow_id"`
	// type of workflow
	WorkflowType WorkflowType `json:"workflow_type"`
	// formance ledger id for transaction flow to separate transaction flows between different ledgers
	SourceLedger string `json:"source_ledger"`
	// source account address pattern that contains "Domain Segments" for payer to match with
	SourceAddressPattern string `json:"source_address_pattern"`
	// destination account address pattern that contains "Domain Segments" for acceptor of the payment to match with
	DestinationAddressPattern string `json:"destination_address_pattern"`
	// priority override for transaction flow
	PriorityOverride int32 `json:"priority_override"`
	// asset for transaction flow to match with
	Asset string `json:"asset"`
	// maximum amount for transaction flow to execute
	UpAmountThreshold float64 `json:"up_amount_threshold"`
	// minimum amount for transaction flow to execute
	DownAmountThreshold float64 `json:"down_amount_threshold"`
	// transaction flow metadatas
	MetaData []byte `json:"meta_data"`
	// workflow binary data for transaction flow execution, it can contains Numscript script or Formance Orchestration workflow yaml file content
	WorkflowData []byte `json:"workflow_data"`
	// when transaction flow was created
	CreatedAt time.Time `json:"created_at"`
	// when transaction flow was updated
	UpdatedAt time.Time `json:"updated_at"`
	// when transaction flow was deleted
	DeletedAt pgtype.Timestamptz `json:"deleted_at"`
}
