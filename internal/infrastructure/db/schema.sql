-- SQL dump generated using DBML (dbml.dbdiagram.io)
-- Database: PostgreSQL
-- Generated at: 2025-08-04T06:13:38.179Z

CREATE TYPE "transaction_draft_type" AS ENUM (
  'MOBILE',
  'NATIONAL_ID',
  'CARD_NUMBER',
  'CARD_TO_CARD'
);

CREATE TYPE "workflow_type" AS ENUM (
  'NUMSCRIPT',
  'WORKFLOW_YAML'
);

CREATE TABLE "transaction_drafts" (
  "id" bigserial PRIMARY KEY,
  "identifier" varchar(64) UNIQUE NOT NULL,
  "draft_type" transaction_draft_type NOT NULL DEFAULT 'MOBILE',
  "totp" varchar(256) UNIQUE NOT NULL,
  "reference_id" varchar(256) UNIQUE NOT NULL,
  "meta_data" jsonb NOT NULL DEFAULT ' {}',
  "card_number" varchar(19) NOT NULL,
  "source_account" varchar(512) NOT NULL,
  "destination_account" varchar(512) NOT NULL,
  "asset" varchar(8) NOT NULL,
  "amount" float NOT NULL,
  "expires_at" timestamptz,
  "updated_at" timestamptz NOT NULL DEFAULT (CURRENT_TIMESTAMP),
  "created_at" timestamptz NOT NULL DEFAULT (CURRENT_TIMESTAMP),
  "deleted_at" timestamptz
);

CREATE TABLE "card_to_card_transaction" (
  "id" bigserial PRIMARY KEY,
  "identifier" varchar(64) UNIQUE NOT NULL,
  "totp" varchar(256) UNIQUE NOT NULL,
  "reference_id" varchar(256) UNIQUE NOT NULL,
  "meta_data" jsonb NOT NULL DEFAULT ' {}',
  "source_card_number" varchar(19) NOT NULL,
  "destination_card_number" varchar(512) NOT NULL,
  "asset" varchar(8) NOT NULL,
  "amount" float NOT NULL,
  "expires_at" timestamptz,
  "updated_at" timestamptz NOT NULL DEFAULT (CURRENT_TIMESTAMP),
  "created_at" timestamptz NOT NULL DEFAULT (CURRENT_TIMESTAMP),
  "deleted_at" timestamptz
);

CREATE TABLE "workflows" (
  "id" bigserial PRIMARY KEY,
  "identifier" varchar(64) UNIQUE NOT NULL,
  "slug" varchar(256) UNIQUE NOT NULL,
  "description" varchar(256) NOT NULL,
  "is_active" boolean NOT NULL DEFAULT 'false',
  "allowed" boolean NOT NULL DEFAULT 'true',
  "workflow_id" varchar(256) NOT NULL,
  "workflow_type" workflow_type NOT NULL DEFAULT 'NUMSCRIPT',
  "source_ledger" varchar(256) NOT NULL DEFAULT 'default',
  "source_address_pattern" varchar(256) NOT NULL,
  "destination_address_pattern" varchar(256) NOT NULL,
  "priority_override" integer NOT NULL DEFAULT 2147483647,
  "asset" varchar(8) NOT NULL,
  "up_amount_threshold" float NOT NULL DEFAULT 0,
  "down_amount_threshold" float NOT NULL DEFAULT 0,
  "meta_data" jsonb NOT NULL DEFAULT ' {}',
  "workflow_data" bytea NOT NULL,
  "created_at" timestamptz NOT NULL DEFAULT (CURRENT_TIMESTAMP),
  "updated_at" timestamptz NOT NULL DEFAULT (CURRENT_TIMESTAMP),
  "deleted_at" timestamptz
);

CREATE INDEX ON "transaction_drafts" ("id");

CREATE INDEX ON "transaction_drafts" ("identifier");

CREATE INDEX ON "transaction_drafts" ("draft_type");

CREATE INDEX ON "transaction_drafts" ("expires_at");

CREATE INDEX ON "transaction_drafts" ("updated_at");

CREATE INDEX ON "transaction_drafts" ("identifier", "expires_at", "updated_at");

CREATE INDEX ON "card_to_card_transaction" ("id");

CREATE INDEX ON "card_to_card_transaction" ("identifier");

CREATE INDEX ON "card_to_card_transaction" ("expires_at");

CREATE INDEX ON "card_to_card_transaction" ("updated_at");

CREATE INDEX ON "card_to_card_transaction" ("source_card_number");

CREATE INDEX ON "card_to_card_transaction" ("destination_card_number");

CREATE INDEX ON "card_to_card_transaction" ("identifier", "expires_at", "updated_at", "source_card_number", "destination_card_number");

CREATE INDEX ON "workflows" ("id");

CREATE INDEX ON "workflows" ("identifier");

CREATE INDEX ON "workflows" ("slug");

CREATE INDEX ON "workflows" ("is_active");

CREATE INDEX ON "workflows" ("created_at");

CREATE INDEX ON "workflows" ("updated_at");

COMMENT ON COLUMN "transaction_drafts"."id" IS 'draft internal system unique id';

COMMENT ON COLUMN "transaction_drafts"."identifier" IS 'unique external identifier for internal-external system identifier isolation';

COMMENT ON COLUMN "transaction_drafts"."draft_type" IS 'represent type of draft transaction';

COMMENT ON COLUMN "transaction_drafts"."totp" IS 'bcrypted TOTP code for validating transaction during committing it';

COMMENT ON COLUMN "transaction_drafts"."reference_id" IS 'idempotency key for making create draft transaction idempotent';

COMMENT ON COLUMN "transaction_drafts"."meta_data" IS 'transaction metadatas';

COMMENT ON COLUMN "transaction_drafts"."card_number" IS 'card number for card number draft type';

COMMENT ON COLUMN "transaction_drafts"."source_account" IS 'source account address for payer of the payment';

COMMENT ON COLUMN "transaction_drafts"."destination_account" IS 'destination account address for acceptor of the payment';

COMMENT ON COLUMN "transaction_drafts"."asset" IS 'asset for transaction flow to match with';

COMMENT ON COLUMN "transaction_drafts"."amount" IS 'amount for transaction flow to match with';

COMMENT ON COLUMN "transaction_drafts"."expires_at" IS 'when the draft will expires';

COMMENT ON COLUMN "transaction_drafts"."updated_at" IS 'when draft was updated';

COMMENT ON COLUMN "transaction_drafts"."created_at" IS 'when draft was created';

COMMENT ON COLUMN "transaction_drafts"."deleted_at" IS 'when draft was deleted';

COMMENT ON COLUMN "card_to_card_transaction"."id" IS 'card to card transaction internal system unique id';

COMMENT ON COLUMN "card_to_card_transaction"."identifier" IS 'unique external identifier for internal-external system identifier isolation';

COMMENT ON COLUMN "card_to_card_transaction"."totp" IS 'bcrypted TOTP code for validating transaction during committing it';

COMMENT ON COLUMN "card_to_card_transaction"."reference_id" IS 'idempotency key for making create card to card transaction transaction idempotent';

COMMENT ON COLUMN "card_to_card_transaction"."meta_data" IS 'transaction metadatas';

COMMENT ON COLUMN "card_to_card_transaction"."source_card_number" IS 'source card number to send asset';

COMMENT ON COLUMN "card_to_card_transaction"."destination_card_number" IS 'destination card number to send asset';

COMMENT ON COLUMN "card_to_card_transaction"."asset" IS 'asset for transaction flow to match with';

COMMENT ON COLUMN "card_to_card_transaction"."amount" IS 'amount for transaction flow to match with';

COMMENT ON COLUMN "card_to_card_transaction"."expires_at" IS 'when the card to card transaction will expires';

COMMENT ON COLUMN "card_to_card_transaction"."updated_at" IS 'when card to card transaction was updated';

COMMENT ON COLUMN "card_to_card_transaction"."created_at" IS 'when card to card transaction was created';

COMMENT ON COLUMN "card_to_card_transaction"."deleted_at" IS 'when card to card transaction was deleted';

COMMENT ON COLUMN "workflows"."id" IS 'transaction flow internal system unique id';

COMMENT ON COLUMN "workflows"."identifier" IS 'unique external identifier for internal-external system identifier isolation';

COMMENT ON COLUMN "workflows"."slug" IS 'unique slug for transaction flow';

COMMENT ON COLUMN "workflows"."description" IS 'description for transaction flow';

COMMENT ON COLUMN "workflows"."is_active" IS 'is transaction flow active';

COMMENT ON COLUMN "workflows"."allowed" IS 'allow or disallow transaction route';

COMMENT ON COLUMN "workflows"."workflow_id" IS 'formance workflow id for transaction flow';

COMMENT ON COLUMN "workflows"."workflow_type" IS 'type of workflow';

COMMENT ON COLUMN "workflows"."source_ledger" IS 'formance ledger id for transaction flow to separate transaction flows between different ledgers';

COMMENT ON COLUMN "workflows"."source_address_pattern" IS 'source account address pattern that contains "Domain Segments" for payer to match with';

COMMENT ON COLUMN "workflows"."destination_address_pattern" IS 'destination account address pattern that contains "Domain Segments" for acceptor of the payment to match with';

COMMENT ON COLUMN "workflows"."priority_override" IS 'priority override for transaction flow';

COMMENT ON COLUMN "workflows"."asset" IS 'asset for transaction flow to match with';

COMMENT ON COLUMN "workflows"."up_amount_threshold" IS 'maximum amount for transaction flow to execute';

COMMENT ON COLUMN "workflows"."down_amount_threshold" IS 'minimum amount for transaction flow to execute';

COMMENT ON COLUMN "workflows"."meta_data" IS 'transaction flow metadatas';

COMMENT ON COLUMN "workflows"."workflow_data" IS 'workflow binary data for transaction flow execution, it can contains Numscript script or Formance Orchestration workflow yaml file content';

COMMENT ON COLUMN "workflows"."created_at" IS 'when transaction flow was created';

COMMENT ON COLUMN "workflows"."updated_at" IS 'when transaction flow was updated';

COMMENT ON COLUMN "workflows"."deleted_at" IS 'when transaction flow was deleted';
